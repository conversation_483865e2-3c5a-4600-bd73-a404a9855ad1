"""
Base Trainer Interface for Unified Training Framework

This module defines the base interface that all trainers must implement
to ensure consistent training and validation behavior across different methods.
"""

import torch
import torch.optim.lr_scheduler as lr_scheduler
import random
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, List
import logging
import math

logger = logging.getLogger(__name__)


class BaseTrainer(ABC):
    """
    Base trainer interface that all specific trainers must implement.
    
    This ensures consistent behavior across IQL, CQL, IRL and future training methods.
    """
    
    def __init__(self, device: str = 'cuda'):
        self.device = device
        self.schedulers = []  # Store all schedulers for this trainer
    
    def set_epoch(self, epoch: int):
        """
        Set current epoch for training (default implementation does nothing).
        
        Subclasses like IRLTrainer can override this for curriculum learning.
        
        Args:
            epoch: Current training epoch
        """
        pass
    
    def create_scheduler(self, optimizer: torch.optim.Optimizer, config, base_lr: float, total_steps: int):
        """
        Create step-based learning rate scheduler with linear warmup + cosine annealing.
        
        Args:
            optimizer: The optimizer to schedule
            config: Training configuration
            base_lr: Base learning rate (target learning rate after warmup)
            total_steps: Total training steps (required)
            
        Returns:
            Learning rate scheduler
        """
        # Calculate warmup steps (10% of total steps)
        warmup_steps = int(total_steps * 0.1)
        warmup_start_lr = getattr(config, 'warmup_start_lr', 1e-6)
        eta_min = getattr(config, 'cosine_annealing_eta_min', 1e-6)
        
        def lr_lambda(step):
            if step < warmup_steps:
                # Linear warmup: from warmup_start_lr to base_lr
                warmup_progress = step / warmup_steps
                warmup_factor = warmup_start_lr / base_lr
                return warmup_factor + (1.0 - warmup_factor) * warmup_progress
            else:
                # Cosine annealing: from base_lr to eta_min
                cosine_step = step - warmup_steps
                cosine_total_steps = total_steps - warmup_steps
                cosine_progress = cosine_step / cosine_total_steps
                cosine_factor = eta_min / base_lr
                return cosine_factor + (1.0 - cosine_factor) * 0.5 * (1.0 + math.cos(math.pi * cosine_progress))
        
        scheduler = lr_scheduler.LambdaLR(optimizer, lr_lambda=lr_lambda)
        
        # Store scheduler for later stepping
        self.schedulers.append(scheduler)
        return scheduler
    
    def set_total_steps(self, total_steps: int):
        """
        Set total training steps and initialize step-based schedulers.
        Should be implemented by subclasses if they need custom scheduler setup.
        """
        pass
    
    def step_schedulers(self):
        """
        Step all schedulers for this trainer.
        Should be called at the end of each epoch.
        """
        for scheduler in self.schedulers:
            scheduler.step()
    
    def get_current_lrs(self) -> List[float]:
        """
        Get current learning rates from all schedulers.
        
        Returns:
            List of current learning rates
        """
        return [scheduler.get_last_lr()[0] for scheduler in self.schedulers]
        
    @abstractmethod
    def train_step(self, batch_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Execute one training step.
        
        Args:
            batch_data: Preprocessed batch data in unified format
            
        Returns:
            Dictionary of training metrics with keys like:
            - 'q_loss', 'v_loss', 'advantage', 'reward_mean', etc.
        """
        pass
    
    @abstractmethod
    def validate(self, batch_generator, epoch: int, dataset_type: str = 'episode') -> Dict[str, float]:
        """
        Execute validation using distributed batch generator.
        
        This method provides the unified validation interface that works consistently
        with the distributed training data flow used in train_step.
        
        Args:
            batch_generator: Iterator yielding validation batches (from create_distributed_frame_batches)
            epoch: Current epoch number
            dataset_type: 'episode' or 'shm' for preprocessing
            
        Returns:
            Dictionary of validation metrics for this rank's data slice with unified format:
            - Core metrics: 'val/top1_acc', 'val/mrr', 'val/ranking_consistency'
            - Trainer-specific metrics: 'val/q_mean', 'val/v_mean', etc.
            - Sample count: 'val/num_samples' (essential for distributed aggregation)
        """
        pass
    
    @abstractmethod
    def save_checkpoint(self, path: str) -> None:
        """
        Save model checkpoint.
        
        Args:
            path: Path to save the checkpoint
        """
        pass
    
    def load_checkpoint(self, path: str) -> None:
        """
        Load model checkpoint.
        
        Args:
            path: Path to load the checkpoint from
        """
        raise NotImplementedError("load_checkpoint not implemented")
    
    def _compute_unified_validation_metrics(self, gt_actions, q_values_fn, language_batch, obs_batch, state_batch, n_noise=2):
        """
        向量化计算统一验证指标 - 与RTG trainer保持一致的高性能实现。
        
        Args:
            gt_actions: Ground truth actions [B, action_dim]
            q_values_fn: Function to compute Q/reward values for given actions
            language_batch: Language instructions [B, lang_dim]
            obs_batch: Observations dict
            state_batch: State data [B, state_dim]
            n_noise: Number of noise levels to sample (default: 3)
            
        Returns:
            Dictionary with 'top1_acc', 'mrr', 'ranking_consistency' and raw data
        """
        batch_size = gt_actions.size(0)
        
        # Step 1: 向量化生成随机噪声动作 - 使用统一的固定噪声标准差
        # 基于CALVIN action统计优化，与具体trainer保持一致
        noise_std = 0.20  # 统一的噪声标准差，基于真实action统计
        
        # 向量化生成所有噪声
        all_noise = torch.randn(batch_size, n_noise, gt_actions.size(1), device=self.device)  # [B, n_noise, action_dim]
        
        # 应用统一的噪声标准差 [B, n_noise, action_dim]
        all_noise = all_noise * noise_std  # 直接乘以scalar，更简洁
        
        # 抓手维度不加噪声
        all_noise[:, :, -1] = 0
        
        # 生成所有噪声动作 [B, n_noise, action_dim]
        expanded_gt_actions = gt_actions.unsqueeze(1).repeat(1, n_noise, 1)  # [B, n_noise, action_dim]
        all_noisy_actions = expanded_gt_actions + all_noise  # [B, n_noise, action_dim]
        
        # Step 1.5: 计算RMSE距离用于目标排序验证
        # 计算每个噪声动作与GT的RMSE距离 [B, n_noise]
        rmse_distances = torch.sqrt(torch.mean((all_noisy_actions - expanded_gt_actions) ** 2, dim=-1))  # [B, n_noise]
        
        # Step 2: 合并GT和噪声动作为统一tensor进行批量Q值计算
        gt_actions_expanded = gt_actions.unsqueeze(1)  # [B, 1, action_dim]
        all_actions_per_sample = torch.cat([gt_actions_expanded, all_noisy_actions], dim=1)  # [B, 1+n_noise, action_dim]
        
        # 展平为 [B*(1+n_noise), action_dim] 进行批量Q值计算
        total_actions = all_actions_per_sample.view(-1, gt_actions.size(1))  # [B*(1+n_noise), action_dim]
        
        # Step 3: 向量化扩展所有输入数据
        actions_per_sample = 1 + n_noise
        
        # 扩展观察数据
        if isinstance(obs_batch, dict):
            expanded_obs = {}
            for k, v in obs_batch.items():
                expanded_obs[k] = v.repeat_interleave(actions_per_sample, dim=0)
        else:
            expanded_obs = obs_batch.repeat_interleave(actions_per_sample, dim=0)
        
        # 扩展语言数据 - 智能处理tensor和字符串列表两种格式
        if isinstance(language_batch, (list, tuple)):
            # 处理字符串列表格式 (RTG trainer使用)
            expanded_language = []
            for lang_item in language_batch:
                expanded_language.extend([lang_item] * actions_per_sample)
        else:
            # 处理tensor格式 (其他trainer使用)
            expanded_language = language_batch.repeat_interleave(actions_per_sample, dim=0)
        
        # 扩展状态数据
        expanded_state = state_batch.repeat_interleave(actions_per_sample, dim=0) if state_batch is not None else None
        
        # Step 4: 批量计算所有Q值
        all_q_values = q_values_fn(expanded_obs, total_actions, expanded_language, expanded_state)  # [B*(1+n_noise)]
        
        # Step 5: 重新整理结果为 [B, 1+n_noise] 格式
        q_values_reshaped = all_q_values.view(batch_size, actions_per_sample)  # [B, 1+n_noise]
        
        # Step 6: 向量化计算指标
        gt_q = q_values_reshaped[:, 0]  # [B] - GT动作的Q值
        noisy_q = q_values_reshaped[:, 1:]  # [B, n_noise] - 噪声动作的Q值
        
        # 向量化计算GT动作排名
        gt_ranks = (q_values_reshaped > gt_q.unsqueeze(1)).sum(dim=1) + 1  # [B]
        
        # Top-1准确率：GT动作是否获得最高Q值
        top1_correct = (gt_ranks == 1).float()  # [B]
        
        # MRR：倒数排名
        mrr = 1.0 / gt_ranks.float()  # [B]
        
        # 排名一致性：GT最高 + 噪声动作按RMSE距离正确排序
        gt_is_best = (gt_ranks == 1)  # [B]
        
        # 检查噪声动作是否按RMSE距离正确排序（RMSE越小，Q值越高）
        if n_noise > 1:
            # 获取每个样本的RMSE排序索引（从小到大）[B, n_noise]
            _, rmse_sort_indices = torch.sort(rmse_distances, dim=1)  # [B, n_noise]
            
            # 获取每个样本的Q值排序索引（从大到小）[B, n_noise] 
            _, q_sort_indices = torch.sort(noisy_q, dim=1, descending=True)  # [B, n_noise]
            
            # 检查Q值排序是否与RMSE排序一致 [B]
            noisy_correct_order = torch.all(rmse_sort_indices == q_sort_indices, dim=1)  # [B]
        else:
            noisy_correct_order = torch.ones(batch_size, dtype=torch.bool, device=self.device)
        
        ranking_consistency = gt_is_best & noisy_correct_order  # [B]
        
        # 计算平均指标
        top1_acc = top1_correct.mean().item()
        mrr_mean = mrr.mean().item()
        ranking_consistency_mean = ranking_consistency.float().mean().item()
        
        return {
            'top1_acc': top1_acc,
            'mrr': mrr_mean,
            'ranking_consistency': ranking_consistency_mean,
            'total_comparisons': batch_size
        }


def seed_everything(seed: int = 42, rank: int = 0) -> None:
    """
    Set random seeds for reproducibility across all random number generators.
    Uses rank-specific seeds for DDP training to ensure diversity.
    
    Args:
        seed: Base random seed value
        rank: GPU rank for DDP training
    """
    # Use rank-specific seeds to ensure diversity across GPUs
    base_seed = seed + rank
    
    torch.manual_seed(base_seed)
    torch.cuda.manual_seed_all(base_seed)
    np.random.seed(base_seed)
    random.seed(base_seed)
    
    # Ensure deterministic behavior (may reduce performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    logger.info(f"Random seed set to {base_seed} (base: {seed}, rank: {rank}) for reproducibility")