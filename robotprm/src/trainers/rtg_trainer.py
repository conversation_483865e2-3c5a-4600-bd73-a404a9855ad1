"""
RTG (ReturnToGo) Trainer with dual loss functions.

This trainer implements the RTG training approach inspired by the ReinBoT paper,
combining IRL Guided Reward Learning (GRL) loss with RTG expectile regression loss
using a dual-head GR1-based model.

Key features:
- Dual-head GR1 model for IRL reward and RTG reward prediction
- IRL GRL loss for IRL reward head (expert vs non-expert ranking)
- RTG expectile regression loss for RTG reward head (ReturnToGo prediction)
- 4-component dense reward computation
- Integration with existing training framework
"""

import torch
import torch.nn.functional as F
from typing import Dict
import logging
import numpy as np
from dataclasses import dataclass
from trainers.base_trainer import BaseTrainer
from trainers.base_config import BaseConfig
from utils.preprocessing import preprocess_batch_for_training
from tqdm import tqdm
import clip

# 导入损失函数模块
from losses.loss_config import LossConfig, LossFactory

logger = logging.getLogger(__name__)

@dataclass
class RTGConfig(BaseConfig):
    """RTG训练配置 - 与IRL trainer对齐"""
    learning_rate: float = 5e-5
    hidden_size: int = 128
    ranking_loss_weight: float = 0.1
    pairwise_loss_weight: float = 0.05
    consistency_loss_weight: float = 0.02
    update_per_collect: int = 5
    log_every_n_train: int = 10
    store_model_every_n_train: int = 100
    
    # 课程学习参数
    curriculum_learning: bool = True
    noise_scale: float = 0.20              # 统一Gaussian噪声std，与IRL trainer对齐
    
    initial_prob_scale: float = 10.0       # RMSE-based概率计算因子（固定值）
    final_prob_scale: float = initial_prob_scale  # 保持与initial_prob_scale一致
    curriculum_epochs: int = 20            # 课程学习总轮数
    
    # 损失函数配置
    loss_function: str = "bradley_terry"   # 默认使用数值稳定的Bradley-Terry损失
    loss_margin_weight: float = 0.1        # Bradley-Terry边际权重
    loss_cost_threshold: float = 0.3       # 对于reward，选择低reward样本作为"坏"动作
    
    # 向后兼容的GCL损失参数
    prob_epsilon: float = 1e-7
    exp_clamp_max: float = 10.0
    
    # RTG 双损失配置
    enable_rtg_loss: bool = True           # 是否启用RTG损失
    rtg_weight: float = 0.1 if enable_rtg_loss else 0.0               # RTG损失权重
    preference_weight: float = 0.9 if enable_rtg_loss else 1.0        # Preference损失权重
    rtg_tau: float = 0.9                  # RTG expectile参数（ReinBoT paper标准）
    rtg_loss_type: str = "rtg_expectile"  # RTG损失类型
    
    # RTG特定参数
    rtg_gamma: float = 0.99               # Discount factor for RTG computation
    reward_weights: tuple = (0.1, 0.1, 0.01, 0.1)  # Dense reward weights (r1, r2, r3, r4)
    warmup_steps: int = 1000              # Warmup steps for learning rate

class RTGTrainer(BaseTrainer):
    """
    RTG Trainer with dual loss functions.
    
    Combines:
    1. IRL Guided Reward Learning (GRL) loss for IRL reward head
    2. RTG expectile regression loss for RTG reward head
    
    The model uses GR1 backbone with dual prediction heads for IRL reward and RTG reward.
    """
    
    def __init__(self, reward_model, config: RTGConfig):
        super().__init__(device=config.device)
        self.config = config
        self.reward_model = reward_model.to(self.device)
        
        self.optimizer = torch.optim.Adam(self.reward_model.parameters(), lr=config.learning_rate, weight_decay=1e-2)
        
        # 课程学习状态追踪
        self.training_step = 0
        self.current_epoch = 0

        self.scheduler = None
        self.total_steps = None
        
        # 初始化损失函数
        self._init_loss_function()
        
        # 打印训练模式信息
        if config.enable_rtg_loss:
            print(f"🎯 RTG双损失训练模式初始化完成")
            print(f"📊 损失权重: RTG={config.rtg_weight}, Preference={config.preference_weight}")
            print(f"🔧 RTG参数: tau={config.rtg_tau}")
        else:
            print(f"🎯 RTG单损失训练模式初始化完成")
            print(f"📊 损失类型: {config.loss_function}")
    
    def _init_loss_function(self):
        # 创建 Preference Loss 配置（原有的损失函数）
        preference_config = LossConfig(
            loss_type=self.config.loss_function,
            device=self.config.device,
            margin_weight=self.config.loss_margin_weight,
            cost_threshold_percentile=self.config.loss_cost_threshold,
            prob_epsilon=self.config.prob_epsilon,
            exp_clamp_max=self.config.exp_clamp_max
        )
        
        # 创建 Preference Loss 实例
        self.preference_loss_function = LossFactory.create_loss(preference_config)
        
        # 如果启用RTG损失，初始化RTG损失函数
        if self.config.enable_rtg_loss:
            rtg_config = LossConfig(
                loss_type=self.config.rtg_loss_type,
                device=self.config.device,
                rtg_tau=self.config.rtg_tau
            )
            self.rtg_loss_function = LossFactory.create_loss(rtg_config)
            logger.info(f"初始化双损失架构: Preference={self.config.loss_function}, RTG={self.config.rtg_loss_type}")
        else:
            self.rtg_loss_function = None
            logger.info(f"初始化单损失模式: {self.config.loss_function}")
        
        # 保持向后兼容
        self.loss_function = self.preference_loss_function
    
    def set_total_steps(self, total_steps: int):
        """
        设置总训练步数并初始化基于步数的学习率调度器
        """
        self.total_steps = total_steps
        # 创建基于步数的学习率调度器
        self.scheduler = self.create_scheduler(self.optimizer, self.config, self.config.learning_rate, total_steps)
        
    def get_curriculum_parameters(self, epoch: int) -> tuple:  # epoch parameter kept for compatibility
        """
        获取统一的噪声参数 - 与IRL trainer对齐
        
        Args:
            epoch: 当前训练轮数 (未使用，保持接口兼容性)
            
        Returns:
            (current_noise_std, prob_scale): 噪声标准差和概率计算因子
        """
        # 统一使用固定的噪声标准差，与IRL trainer对齐
        return self.config.noise_scale, self.config.initial_prob_scale
    
    def set_epoch(self, epoch: int):
        """设置当前训练轮数，用于课程学习"""
        self.current_epoch = epoch
    
    def _get_rtg_labels_from_batch(self, batch_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        从批次数据中获取RTG标签
        
        Args:
            batch_data: 包含预计算RTG标签的批次数据
            
        Returns:
            torch.Tensor: RTG标签 [B]
        """
        rtg_labels = batch_data['rtg_label']
            
        return rtg_labels.to(self.device)
    
    def _call_rtg_model(self, obs_batch, action_batch, language_batch, state_batch):
        """
        RTG模型特定的调用方式 - 唯一与IRL trainer不同的部分
        
        Returns:
            torch.Tensor: [B] reward predictions
        """
        # 准备RTG模型输入格式
        rgb_static = obs_batch['rgb_static'].unsqueeze(1)
        rgb_gripper = obs_batch['rgb_gripper'].unsqueeze(1)
        
        # 处理state格式
        state_dict = {
            'arm': state_batch[:, :-1].unsqueeze(1),
            'gripper': state_batch[:, -1:].unsqueeze(1)
        }
        
        # Convert gripper to one-hot
        batch_size = state_batch.size(0)
        gripper_onehot = torch.zeros(batch_size, 1, 2, device=self.device)
        gripper_onehot[:, :, 0] = 1 - state_dict['gripper'].squeeze(-1)
        gripper_onehot[:, :, 1] = state_dict['gripper'].squeeze(-1)
        state_dict['gripper'] = gripper_onehot
        
        # 处理语言输入
        if isinstance(language_batch, (list, tuple)) and isinstance(language_batch[0], str):
            language_tokens = clip.tokenize(language_batch).to(self.device)
        else:
            language_tokens = language_batch.long().to(self.device) if hasattr(language_batch, 'long') else language_batch
        
        action_input = action_batch.unsqueeze(1)
        attention_mask = torch.ones(batch_size, 1, device=self.device)
        
        # RTG模型调用
        reward_pred = self.reward_model(
            rgb=rgb_static,
            hand_rgb=rgb_gripper,
            state=state_dict,
            language=language_tokens,
            action=action_input,
            attention_mask=attention_mask
        )
        
        return reward_pred.squeeze(-1)
    
    def train_step(self, batch_data) -> Dict[str, float]:
        """
        执行一步RTG双损失训练：RTG预测 + Preference学习 - 与IRL trainer对齐的结构

        双损失架构：
        - 专家动作：RTG标签预测（RTG loss）
        - 噪声动作：偏好学习损失（Preference loss）
        - 组合损失：rtg_weight * RTG_loss + preference_weight * Preference_loss
        
        Args:
            batch_data: 标准批次数据（包含RTG标签）
            
        Returns:
            训练统计信息
        """
        # 从batch中提取数据 - 与IRL trainer完全一致
        language_instruction = batch_data['language_instruction']  # [B, language_dim]
        current_observation = batch_data['current_observation']  
        expert_action = batch_data['action']  # [B, action_dim]
        batch_size = expert_action.shape[0]
        num_noise_samples = 2
        
        # 获取课程学习参数
        current_noise_std, prob_scale = self.get_curriculum_parameters(self.current_epoch)
        self.training_step += 1
        
        # 步骤1: 获取RTG标签（如果启用RTG损失）
        rtg_labels = None
        if self.config.enable_rtg_loss:
            rtg_labels = self._get_rtg_labels_from_batch(batch_data)  # [B]
        
        # ===== 向量化生成噪声采样数据 =====
        expanded_expert = expert_action.repeat_interleave(num_noise_samples, dim=0)  # [B*N, action_dim]
        
        # 改进的Gaussian噪声生成：直接使用统一的标准差
        noise = torch.randn_like(expanded_expert) * current_noise_std  # [B*N, action_dim]
        
        # gripper维度（最后一维）不加噪声
        noise[:, -1] = 0  # [B*N] gripper控制保持不变
        
        # 生成噪声动作（无边界约束）
        noisy_actions = expanded_expert + noise  # [B*N, action_dim]
        
        # 批量计算RMSE距离和概率 - 使用课程学习的概率因子
        rmse_distances = torch.sqrt(torch.mean((expanded_expert - noisy_actions) ** 2, dim=1))  # [B*N]
        sample_probs = torch.exp(-prob_scale * rmse_distances).clamp(0.01, 0.9)  # [B*N]
        
        # ===== 优化：合并专家动作和噪声动作进行一次推理 =====
        # 步骤2: 合并所有动作 [B + B*N, action_dim]
        all_actions = torch.cat([expert_action, noisy_actions], dim=0)  # [B(1+N), action_dim]
        
        # 步骤3: 扩展观察数据以匹配所有动作（适配RTG模型格式）
        # 专家动作部分：[B, ...]
        expert_obs = {
            'rgb_static': current_observation['obs']['rgb_static'],  # [B, ...]
            'rgb_gripper': current_observation['obs']['rgb_gripper']  # [B, ...]
        }
        expert_language = batch_data['language_raw_text']  # [B] list of strings - RTG需要原始文本
        expert_state = current_observation['state']  # [B, state_dim]
        
        # 噪声动作部分：[B*N, ...]  
        noisy_obs = {
            'rgb_static': current_observation['obs']['rgb_static'].repeat_interleave(num_noise_samples, dim=0),  # [B*N, ...]
            'rgb_gripper': current_observation['obs']['rgb_gripper'].repeat_interleave(num_noise_samples, dim=0)  # [B*N, ...]
        }
        noisy_language = batch_data['language_raw_text'] * num_noise_samples  # [B*N] list of strings - RTG需要原始文本
        noisy_state = current_observation['state'].repeat_interleave(num_noise_samples, dim=0)  # [B*N, state_dim]
        
        # 合并观察数据
        combined_obs = {
            'rgb_static': torch.cat([expert_obs['rgb_static'], noisy_obs['rgb_static']], dim=0),
            'rgb_gripper': torch.cat([expert_obs['rgb_gripper'], noisy_obs['rgb_gripper']], dim=0)
        }
        combined_language = expert_language + noisy_language  # list concatenation
        combined_state = torch.cat([expert_state, noisy_state], dim=0)
        
        # 步骤4: 一次性推理所有动作（使用RTG模型格式）
        all_reward_predictions = self._call_rtg_model(
            combined_obs,      # [B(1+N), ...]
            all_actions,       # [B(1+N), action_dim]
            combined_language, # [B(1+N)] list of strings
            combined_state     # [B(1+N), state_dim]
        )  # [B(1+N)] RTG reward predictions
        
        # 步骤5: 拆分推理结果

        expert_rtg_predictions = all_reward_predictions[:batch_size]   # [B] - 专家动作的reward预测值（same as expert_predictions）
        sample_rewards = all_reward_predictions[batch_size:]          # [B*N] - 噪声动作的reward预测值
        
        # 步骤6: 计算双损失
        rtg_loss = torch.zeros(1, device=self.device)
        preference_loss = torch.zeros(1, device=self.device)
        
        # RTG损失：专家动作预测RTG标签
        if self.config.enable_rtg_loss and rtg_labels is not None:
            rtg_output = self.rtg_loss_function.compute_loss(
                reward_pred=expert_rtg_predictions,  # 使用RTG头的预测
                rtg_targets=rtg_labels
            )
            rtg_loss = rtg_output.loss
        
        # Preference损失：噪声动作偏好学习
        batch_data_for_loss = {
            'batch_size': batch_size,
            'rmse_distances': rmse_distances
        }
        
        preference_output = self.preference_loss_function.compute_loss(
            sample_rewards=sample_rewards,
            batch_data=batch_data_for_loss
        )
        preference_loss = preference_output.loss
        
        # 步骤7: 组合损失
        total_loss = self.config.rtg_weight * rtg_loss + self.config.preference_weight * preference_loss

        # 反向传播 - 与IRL trainer完全一致
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 改进的梯度裁剪：更保守的裁剪值，并监控梯度范数
        grad_norm = torch.nn.utils.clip_grad_norm_(self.reward_model.parameters(), 1.0)
        self.optimizer.step()

        # 监督：检查梯度流动和更新步数计数器 (处理DDP包装)
        self.reward_model.module.training_step_monitor()
        
        # 定期清理GPU缓存，防止内存碎片化
        if self.training_step % 100 == 0:
            torch.cuda.empty_cache()
        
        # 获取当前学习率
        current_lrs = self.get_current_lrs()
        
        # 步骤8: 统计信息 - 双损失架构（与IRL trainer对齐）
        # 从损失函数获取指标
        preference_metrics = preference_output.metrics
        rtg_metrics = rtg_output.metrics if self.config.enable_rtg_loss and rtg_labels is not None else {}
        
        # 准备返回的统计信息 - 只保留5个核心训练指标
        stats = {
            # 核心训练指标 (Top 5)
            'train/total_loss': total_loss.item(),
            'train/preference_loss': preference_loss.item(),
            'train/rtg_loss': rtg_loss.item(),
            'train/learning_rate': current_lrs[0] if len(current_lrs) >= 1 else 0.0,
            'train/grad_norm': grad_norm.item() if grad_norm is not None else 0.0,
        }
        
        # Legacy compatibility fields (for existing framework compatibility)
        main_loss = preference_metrics.get('gcl_loss', preference_metrics.get('bt_loss', preference_loss.item()))
        
        stats.update({
            'q_loss': main_loss,
            'v_loss': rtg_loss.item(),
            'advantage': expert_rtg_predictions.mean().item() if expert_rtg_predictions is not None else 0.0,
            'reward_mean': rtg_labels.mean().item() if rtg_labels is not None else 0.0,
        })
        
        return stats
    
    def validate(self, batch_generator, epoch: int, dataset_type: str = 'episode', valida_len: int = 0) -> Dict[str, float]:
        """
        执行RTG验证 - 使用统一验证指标（与IRL trainer对齐）
        
        Args:
            batch_generator: Iterator yielding validation batches (from create_distributed_frame_batches)
            epoch: Current epoch number
            dataset_type: 'episode' or 'shm' for preprocessing
            
        Returns:
            Dictionary with validation metrics for this rank's data slice
        """
        # 设置为评估模式
        self.reward_model.eval()
        
        total_reward = 0
        total_rtg_reward = 0
        num_samples = 0
        
        # Core unified metrics
        all_top1_acc = []
        all_mrr = []
        all_ranking_consistency = []
        
        with torch.no_grad():
            pbar = tqdm(batch_generator, total=valida_len, desc="Validating", ncols=100, leave=False)
            for batch_data in pbar:
                processed_batch = preprocess_batch_for_training(batch_data, device=self.device, dataset_type=dataset_type)
                
                if processed_batch is None:
                    continue
                
                # 计算专家动作的rewards（RTG模型输出reward预测）
                current_reward = self._call_rtg_model(
                    processed_batch['current_observation']['obs'],
                    processed_batch['action'],
                    processed_batch['language_raw_text'],
                    processed_batch['current_observation']['state']
                )  # [B] reward predictions
                current_rtg_reward = current_reward  # Same as current_reward since we only have one output
                
                # Accumulate basic statistics
                batch_size = current_reward.size(0)
                total_reward += current_reward.sum().item()
                total_rtg_reward += current_rtg_reward.sum().item()
                num_samples += batch_size
                
                # 使用基类的统一验证指标计算方法
                def q_values_fn(obs, action, language, state):
                    """Helper function for unified validation - RTG特定的Q值计算"""
                    return self._call_rtg_model(obs, action, language, state)  # 使用reward作为主要排序指标
                
                unified_metrics = self._compute_unified_validation_metrics(
                    gt_actions=processed_batch['action'],
                    q_values_fn=q_values_fn,
                    language_batch=processed_batch['language_raw_text'],
                    obs_batch=processed_batch['current_observation']['obs'],
                    state_batch=processed_batch['current_observation']['state'],
                    n_noise=2
                )
                
                all_top1_acc.append(unified_metrics['top1_acc'])
                all_mrr.append(unified_metrics['mrr'])
                all_ranking_consistency.append(unified_metrics['ranking_consistency'])
        
        # Calculate final metrics for this rank
        avg_reward_mean = total_reward / num_samples if num_samples > 0 else 0.0
        avg_rtg_reward_mean = total_rtg_reward / num_samples if num_samples > 0 else 0.0
        
        # Calculate unified metrics for this rank
        final_top1_acc = np.mean(all_top1_acc) if all_top1_acc else 0.0
        final_mrr = np.mean(all_mrr) if all_mrr else 0.0
        final_ranking_consistency = np.mean(all_ranking_consistency) if all_ranking_consistency else 0.0
        
        # Return to training mode
        self.reward_model.train()
        
        # 返回带有val/前缀的验证指标（与IRL trainer对齐）
        return {
            # 核心验证指标 (Top 5)
            'val/top1_acc': final_top1_acc,
            'val/mrr': final_mrr,
            'val/ranking_consistency': final_ranking_consistency,
            'val/reward_mean': avg_reward_mean,           # Reward predictions
            'val/num_samples': num_samples,  # Essential for proper aggregation
            
            # Legacy compatibility fields
            'val/rtg_reward_mean': avg_rtg_reward_mean,   # RTG reward predictions
            'val/expert_reward_mean': avg_reward_mean,    # Reward assigned to expert actions
            'val/q_mean': avg_reward_mean,                # Convert to reward-like for compatibility
            'val/epoch': epoch,
            'v_mean': avg_rtg_reward_mean,  # RTG reward mean
            'advantage': avg_reward_mean + avg_rtg_reward_mean,  # Combined advantage
        }
    
    def save_checkpoint(self, path):
        """保存模型检查点 - 只保存权重，避免复杂对象引用"""
        checkpoint = {
            'reward_model': self.reward_model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
        }
        torch.save(checkpoint, path)
        
    def save_weights_only(self, path):
        """只保存模型权重，可以安全加载"""
        weights = {
            'reward_model': self.reward_model.state_dict(),
        }
        torch.save(weights, path)
    
    def load_checkpoint(self, path):
        """加载模型检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.reward_model.load_state_dict(checkpoint['reward_model'])
        
        # 只有在checkpoint包含优化器状态时才加载
        if 'optimizer' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer'])
            
    def load_weights_only(self, path):
        """只加载模型权重"""
        weights = torch.load(path, map_location=self.device, weights_only=True)
        
        self.reward_model.load_state_dict(weights['reward_model'])