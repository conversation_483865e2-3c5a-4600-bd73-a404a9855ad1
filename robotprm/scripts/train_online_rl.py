#!/usr/bin/env python3
"""
Online RL Training Script for Reward Model Training.

This script implements online reinforcement learning training where:
1. GR1 Policy Model generates actions (frozen or trainable)
2. CALVIN environment executes actions with exploration noise
3. Reward Model learns from trajectory success/failure patterns
4. System discovers new successful strategies through action perturbation

Usage:
    python scripts/train_online_rl.py --config configs/online_rl_config.yaml

Key features:
- Simple fail-fast design with minimal validation
- Multiple exploration strategies (Gaussian, multi-modal, curriculum)
- Trajectory diversity analysis and novelty detection
- Real-time monitoring and checkpoint saving
- Integration with existing reward models and preprocessing
"""

import argparse
import logging
import time
from pathlib import Path
import yaml
import torch
import numpy as np
from typing import Optional

# Add src directory to path for imports
import sys
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

from src.trainers.online_rl_trainer import OnlineRLTrainer, OnlineRLConfig
from src.utils.exploration_strategies import create_exploration_strategy, ExplorationConfig, TrajectoryDiversityAnalyzer


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration."""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)


def load_config(config_path: str) -> dict:
    """
    Load configuration from YAML file with simple validation.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        config: Loaded configuration dictionary
    """
    config_path = Path(config_path)
    
    assert config_path.exists(), f"Configuration file not found: {config_path}"
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    assert config is not None, f"Configuration file is empty or invalid: {config_path}"
    
    return config


def validate_and_prepare_config(config: dict, args: argparse.Namespace) -> OnlineRLConfig:
    """
    Validate configuration and prepare OnlineRLConfig object.
    
    Args:
        config: Raw configuration dictionary
        args: Command line arguments
        
    Returns:
        online_rl_config: Validated configuration object
    """
    logger = logging.getLogger("ConfigValidator")
    
    # Override config with command line arguments
    if args.policy_path:
        config['policy']['model_path'] = args.policy_path
    if args.task_name:
        config['environment']['task_name'] = args.task_name
    if args.num_episodes:
        config['training']['num_episodes'] = args.num_episodes
    if args.device:
        config['training']['device'] = args.device
    
    # Simple validation - fail fast if keys missing
    assert 'policy' in config, "Missing 'policy' section in config"
    assert 'reward' in config, "Missing 'reward' section in config"
    assert 'training' in config, "Missing 'training' section in config"
    assert 'exploration' in config, "Missing 'exploration' section in config"
    assert 'environment' in config, "Missing 'environment' section in config"
    assert 'rewards' in config, "Missing 'rewards' section in config"
    
    # Validate policy model path exists
    policy_path = config['policy']['model_path']
    assert Path(policy_path).exists(), f"Policy model file not found: {policy_path}"
    
    logger.info("✅ Configuration validation completed successfully")
    
    # Create OnlineRLConfig object
    online_rl_config = OnlineRLConfig(
        # Policy configuration
        policy_model_path=config['policy']['model_path'],
        policy_frozen=config['policy'].get('frozen', True),
        policy_update_frequency=config['policy'].get('update_frequency', 10),
        
        # Reward model configuration
        reward_model_type=config['reward']['type'],
        reward_model_path=config['reward'].get('model_path'),
        reward_update_frequency=config['reward'].get('update_frequency', 1),
        
        # Training configuration
        num_episodes=config['training']['num_episodes'],
        max_episode_length=config['training']['max_episode_length'],
        batch_size=config['training']['batch_size'],
        learning_rate=config['training']['learning_rate'],
        
        # Exploration configuration
        action_noise_scale=config['exploration']['initial_noise_scale'],
        noise_decay_rate=config['exploration']['noise_decay_rate'],
        min_noise_scale=config['exploration']['min_noise_scale'],
        
        # Reward configuration
        reward_success=config['rewards']['success'],
        reward_failure=config['rewards']['failure'],
        reward_step=config['rewards']['step'],
        
        # Environment configuration
        task_name=config['environment']['task_name'],
        num_parallel_envs=config['environment'].get('num_parallel_envs', 1),
        use_language=config['environment'].get('use_language', True),
        
        # Training stability
        gradient_clip_norm=config['training'].get('gradient_clip_norm', 1.0),
        target_network_update_freq=config['training'].get('target_network_update_freq', 100),
        
        # Logging
        log_frequency=config['logging'].get('frequency', 10),
        save_frequency=config['logging'].get('save_frequency', 100),
        
        # Device
        device=config['training']['device']
    )
    
    return online_rl_config


def create_exploration_manager(config: dict) -> tuple:
    """
    Create exploration strategy and diversity analyzer.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        exploration_strategy: Configured exploration strategy
        diversity_analyzer: Trajectory diversity analyzer
    """
    # Create exploration configuration
    exploration_config = ExplorationConfig(
        initial_noise_scale=config['exploration']['initial_noise_scale'],
        min_noise_scale=config['exploration']['min_noise_scale'],
        max_noise_scale=config['exploration']['max_noise_scale'],
        noise_decay_rate=config['exploration']['noise_decay_rate'],
        success_threshold=config['exploration'].get('success_threshold', 0.7),
        failure_threshold=config['exploration'].get('failure_threshold', 0.3),
        adaptation_window=config['exploration'].get('adaptation_window', 50),
        enable_curriculum=config['exploration'].get('enable_curriculum', True)
    )
    
    # Create exploration strategy
    strategy_type = config['exploration'].get('strategy_type', 'gaussian')
    exploration_strategy = create_exploration_strategy(strategy_type, exploration_config)
    
    # Create diversity analyzer
    diversity_analyzer = TrajectoryDiversityAnalyzer(
        diversity_threshold=config['exploration'].get('diversity_threshold', 0.1)
    )
    
    return exploration_strategy, diversity_analyzer


def run_training(
    trainer: OnlineRLTrainer,
    exploration_strategy,
    diversity_analyzer: TrajectoryDiversityAnalyzer
):
    """
    Run the main training loop with monitoring and analysis.
    
    Args:
        trainer: Configured online RL trainer
        exploration_strategy: Exploration strategy for action noise
        diversity_analyzer: Trajectory diversity analyzer
    """
    logger = logging.getLogger("OnlineRLTraining")
    
    logger.info("🚀 Starting online RL training...")
    logger.info(f"  - Task: {trainer.config.task_name}")
    logger.info(f"  - Episodes: {trainer.config.num_episodes}")
    logger.info(f"  - Policy frozen: {trainer.config.policy_frozen}")
    logger.info(f"  - Reward model: {trainer.config.reward_model_type}")
    logger.info(f"  - Device: {trainer.config.device}")
    
    # Training statistics
    start_time = time.time()
    episode_rewards = []
    success_rates = []
    diversity_metrics = []
    
    # Enhanced training loop with exploration and diversity analysis
    for episode in range(trainer.config.num_episodes):
        # Run episode with current exploration strategy
        episode_info = trainer.run_episode()
        
        # Get recent trajectory for analysis
        recent_trajectories = trainer.trajectory_buffer.get_recent_trajectories(1)
        if recent_trajectories:
            trajectory = recent_trajectories[0]
            
            # Add to diversity analyzer
            diversity_analyzer.add_trajectory(trajectory['steps'], trajectory['success'])
            
            # Check if trajectory is novel
            is_novel = diversity_analyzer.is_trajectory_novel(trajectory['steps'])
            if is_novel and trajectory['success']:
                logger.info(f"🌟 Novel successful strategy discovered in episode {episode}!")
        
        # Update exploration strategy
        success_rate = len(trainer.trajectory_buffer.get_successful_trajectories()) / max(1, len(trainer.trajectory_buffer.trajectories))
        exploration_strategy.update(success_rate, episode)
        
        # Update trainer's noise scale from exploration strategy
        if hasattr(exploration_strategy, 'current_noise_scale'):
            trainer.current_noise_scale = exploration_strategy.current_noise_scale
        elif hasattr(exploration_strategy, 'global_scale'):
            trainer.current_noise_scale = exploration_strategy.global_scale
        
        # Collect statistics
        episode_rewards.append(episode_info['episode_reward'])
        success_rates.append(success_rate)
        
        # Train reward model
        if episode % trainer.config.reward_update_frequency == 0:
            recent_trajectories = trainer.trajectory_buffer.get_recent_trajectories(100)
            if len(recent_trajectories) > 10:
                trainer.train_reward_model(recent_trajectories)
        
        # Logging and analysis
        if episode % trainer.config.log_frequency == 0:
            # Compute diversity metrics
            diversity_metrics_dict = diversity_analyzer.compute_diversity_metrics()
            diversity_metrics.append(diversity_metrics_dict)
            
            # Enhanced logging
            avg_reward = np.mean(episode_rewards[-trainer.config.log_frequency:])
            recent_success_rate = np.mean(success_rates[-trainer.config.log_frequency:])
            
            logger.info(
                f"Episode {episode:4d}: "
                f"Reward={episode_info['episode_reward']:6.2f} "
                f"(avg={avg_reward:6.2f}), "
                f"Length={episode_info['episode_length']:3d}, "
                f"Success={episode_info['success']}, "
                f"SR={recent_success_rate:.3f}, "
                f"Noise={trainer.current_noise_scale:.3f}, "
                f"Diversity={diversity_metrics_dict['diversity']:.3f}, "
                f"Novelty={diversity_metrics_dict['novelty']:.3f}"
            )
            
            # Log exploration statistics
            if episode % (trainer.config.log_frequency * 5) == 0:
                logger.info(
                    f"Exploration Stats - "
                    f"Total trajectories: {diversity_metrics_dict['num_trajectories']}, "
                    f"Successful: {diversity_metrics_dict['num_successful']}, "
                    f"Success diversity: {diversity_metrics_dict['success_diversity']:.3f}"
                )
        
        # Save checkpoint
        if episode % trainer.config.save_frequency == 0 and episode > 0:
            trainer.save_checkpoint(episode)
            
            # Save additional statistics
            stats = {
                'episode_rewards': episode_rewards,
                'success_rates': success_rates,
                'diversity_metrics': diversity_metrics,
                'training_time': time.time() - start_time
            }
            torch.save(stats, f"online_rl_stats_episode_{episode}.pth")
    
    # Final statistics
    elapsed_time = time.time() - start_time
    final_success_rate = success_rates[-1] if success_rates else 0.0
    avg_reward = np.mean(episode_rewards) if episode_rewards else 0.0
    
    logger.info("🏁 Training completed!")
    logger.info(f"  - Total time: {elapsed_time:.1f} seconds")
    logger.info(f"  - Final success rate: {final_success_rate:.3f}")
    logger.info(f"  - Average reward: {avg_reward:.2f}")
    logger.info(f"  - Total trajectories: {len(trainer.trajectory_buffer.trajectories)}")
    logger.info(f"  - Successful trajectories: {len(trainer.trajectory_buffer.get_successful_trajectories())}")
    
    # Save final checkpoint and statistics
    trainer.save_checkpoint(trainer.config.num_episodes)
    final_stats = {
        'episode_rewards': episode_rewards,
        'success_rates': success_rates,
        'diversity_metrics': diversity_metrics,
        'final_success_rate': final_success_rate,
        'average_reward': avg_reward,
        'training_time': elapsed_time,
        'config': trainer.config.__dict__
    }
    torch.save(final_stats, "online_rl_final_stats.pth")


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Online RL Training for Reward Model")
    
    # Required arguments
    parser.add_argument(
        '--config', 
        type=str, 
        required=True,
        help='Path to training configuration YAML file'
    )
    
    # Optional overrides
    parser.add_argument(
        '--policy-path',
        type=str,
        help='Override policy model path from config'
    )
    parser.add_argument(
        '--task-name',
        type=str,
        help='Override task name from config'
    )
    parser.add_argument(
        '--num-episodes',
        type=int,
        help='Override number of episodes from config'
    )
    parser.add_argument(
        '--device',
        type=str,
        choices=['cpu', 'cuda'],
        help='Override device from config'
    )
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Logging level'
    )
    parser.add_argument(
        '--log-file',
        type=str,
        help='Optional log file path'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger("OnlineRLMain")
    
    logger.info("🎯 Online RL Training for Reward Model")
    logger.info(f"Config: {args.config}")
    
    try:
        # Load and validate configuration
        config = load_config(args.config)
        online_rl_config = validate_and_prepare_config(config, args)
        
        # Create exploration manager
        exploration_strategy, diversity_analyzer = create_exploration_manager(config)
        
        # Create trainer
        trainer = OnlineRLTrainer(online_rl_config)
        
        # Run training
        run_training(trainer, exploration_strategy, diversity_analyzer)
        
        logger.info("✅ Training completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise
    
    return 0


if __name__ == "__main__":
    exit(main())