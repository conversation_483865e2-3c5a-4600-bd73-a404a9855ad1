#!/usr/bin/env python3
"""
RTG Debug Wrapper Script

This script calls run_rtg_train.sh with debug parameters equivalent to:
./run_rtg_train.sh --per_gpu_bs 128 --debug

This allows VS Code debugging without requiring terminal plugins.
"""

import subprocess
import sys
import os

def main():
    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shell_script = os.path.join(script_dir, "run_rtg_train.sh")
    
    # RTG training arguments equivalent to: ./run_rtg_train.sh --per_gpu_bs 128 --debug
    args = [
        "bash", shell_script,
        "--gpus", "0",
        "--world_size", "1", 
        "--per_gpu_bs", "128",
        "--epochs", "30",
        "--debug",
        "--debug_max_episodes", "50",
        "--mae_ckpt", "/mnt/disk2/surui/BoostingVLA/GR-1/logs/mae_pretrain_vit_base.pth",
        "--pretrained_gr1_path", "/mnt/disk2/surui/BoostingVLA/GR-1/logs/snapshot_ABC.pt"
    ]
    
    print("🚀 Starting RTG training via shell script...")
    print(f"Command: {' '.join(args)}")
    print("=" * 50)
    
    try:
        # Run the shell script with the arguments
        result = subprocess.run(args, check=True, text=True)
        print("\n✅ RTG training completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n❌ RTG training failed with exit code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print(f"❌ Shell script not found: {shell_script}")
        print("Please make sure run_rtg_train.sh exists in the scripts directory.")
        return 1
    except KeyboardInterrupt:
        print("\n⚠️ RTG training interrupted by user")
        return 130

if __name__ == "__main__":
    sys.exit(main()) 