#!/usr/bin/env python3
"""
简单测试脚本，验证修复后的代码是否符合编码规范。
"""

import ast
import sys
from pathlib import Path

def check_no_try_except(file_path):
    """检查文件中是否没有try/except语句"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"❌ 语法错误在 {file_path}: {e}")
        return False
    
    for node in ast.walk(tree):
        if isinstance(node, ast.Try):
            print(f"❌ 发现try/except语句在 {file_path}:行{node.lineno}")
            return False
    
    print(f"✅ {file_path}: 没有try/except语句")
    return True

def check_no_dict_get(file_path):
    """检查文件中是否没有dict.get()调用"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"❌ 语法错误在 {file_path}: {e}")
        return False
    
    for node in ast.walk(tree):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute) and node.func.attr == 'get':
                print(f"❌ 发现dict.get()调用在 {file_path}:行{node.lineno}")
                return False
    
    print(f"✅ {file_path}: 没有dict.get()调用")
    return True

def check_imports(file_path):
    """检查导入是否正确"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
        print(f"✅ {file_path}: 导入语法正确")
        return True
    except SyntaxError as e:
        print(f"❌ 导入语法错误在 {file_path}: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始验证修复后的代码...")
    
    files_to_check = [
        "robotprm/src/trainers/online_rl_trainer.py",
        "robotprm/scripts/train_online_rl.py"
    ]
    
    all_passed = True
    
    for file_path in files_to_check:
        print(f"\n📁 检查文件: {file_path}")
        
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            all_passed = False
            continue
        
        # 检查编码规范
        if not check_no_try_except(file_path):
            all_passed = False
        
        if not check_no_dict_get(file_path):
            all_passed = False
        
        if not check_imports(file_path):
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有检查通过！代码符合编码规范。")
        return 0
    else:
        print("❌ 部分检查失败，请修复问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
